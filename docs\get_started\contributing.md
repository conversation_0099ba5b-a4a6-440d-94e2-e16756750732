# Guidlines for contributing to the project

We welcome contributions to the Director from developers, researchers, and enthusiasts interested in video processing, AI, and related fields. This document outlines the guidelines for contributing to the project, including the process for submitting issues, feature requests, and pull requests.

Any contributions you make are **greatly appreciated**. Here's the process:

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request