[project]
name = "director"
version = "0.1.0"
description = "Director backend."
readme = "README.md"
requires-python = ">=3.6"
dependencies = [
    "flask==3.0.3",
    "flask-socketio==5.3.6",
    "flask-cors==4.0.1",
    "openai==1.55.3",
    "pydantic==2.8.2",
    "pydantic-settings==2.4.0",
    "python-dotenv==1.0.1",
    "videodb",
    "yt-dlp==2024.10.7",
    "director",
]


[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"
