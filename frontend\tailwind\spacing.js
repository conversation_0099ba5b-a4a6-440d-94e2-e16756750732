/**
 * Tailwind CSS Spacing Configuration
 * Extracted from main tailwind.config.js for better maintainability
 */

export const spacing = {
  1: "1px",
  2: "0.125rem",
  3: "0.188rem",
  4: "0.25rem",
  6: "0.375rem",
  8: "0.5rem",
  10: "0.625rem",
  12: "0.75rem",
  14: "0.875rem",
  16: "1rem",
  18: "1.125rem",
  20: "1.25rem",
  22: "1.375rem",
  24: "1.5rem",
  25: "1.563rem",
  28: "1.75rem",
  30: "1.875rem",
  32: "2rem",
  34: "2.125rem",
  38: "2.375rem",
  40: "2.5rem",
  44: "2.75rem",
  42: "2.625rem",
  46: "2.875rem",
  48: "3rem",
  56: "3.5rem",
  60: "3.75rem",
  64: "4rem",
  72: "4.5rem",
  76: "4.75rem",
  80: "5rem",
  88: "5.5rem",
  96: "6rem",
  100: "6.25rem",
  104: "6.5rem",
  106: "6.625",
  112: "7rem",
  124: "7.75rem",
  128: "8rem",
  136: "8.5rem",
  142: "8.875rem",
  152: "9.5rem",
  168: "10.5rem",
  176: "11rem",
  180: "11.25rem",
  182: "11.375rem",
  185: "11.563rem",
  200: "12.5rem",
  224: "14rem",
  240: "15rem",
  256: "16rem",
  288: "18rem",
  294: "18.375rem",
  326: "20.375rem",
  330: "20.625rem",
  352: "22rem",
  372: "23.25rem",
  400: "25rem",
  432: "27rem",
  512: "32rem",
  580: "36.25rem",
  640: "40rem",
  700: "43.75rem",
  fit: "fit-content",
};

export const minWidth = {
  1: "1px",
  2: "0.125rem",
  3: "0.188rem",
  4: "0.25rem",
  6: "0.375rem",
  8: "0.5rem",
  12: "0.75rem",
  14: "0.875rem",
  16: "1rem",
  18: "1.125rem",
  20: "1.25rem",
  24: "1.5rem",
  25: "1.563rem",
  28: "1.75rem",
  32: "2rem",
  34: "2.125rem",
  40: "2.5rem",
  42: "2.625rem",
  44: "2.75rem",
  48: "3rem",
  56: "3.5rem",
  60: "3.75rem",
  64: "4rem",
  72: "4.5rem",
  76: "4.75rem",
  80: "5rem",
  88: "5.5rem",
  96: "6rem",
  100: "6.25rem",
  104: "6.5rem",
  106: "6.625",
  112: "7rem",
  128: "8rem",
  136: "8.5rem",
  142: "8.875rem",
  152: "9.5rem",
  168: "10.5rem",
  176: "11rem",
  182: "11.375rem",
  185: "11.563rem",
  200: "12.5rem",
  352: "22rem",
  220: "13.75rem",
  250: "15.625rem",
  400: "25rem",
  432: "27rem",
  512: "32rem",
  640: "40rem",
  "1/4": "25%",
  "1/2": "50%",
  "3/4": "75%",
  full: "100%",
};

export const maxWidth = {
  1: "1px",
  2: "0.125rem",
  4: "0.25rem",
  6: "0.375rem",
  8: "0.5rem",
  12: "0.75rem",
  14: "0.875rem",
  16: "1rem",
  18: "1.125rem",
  20: "1.25rem",
  24: "1.5rem",
  28: "1.75rem",
  32: "2rem",
  34: "2.125rem",
  40: "2.5rem",
  42: "2.625rem",
  44: "2.75rem",
  48: "3rem",
  56: "3.5rem",
  60: "3.75rem",
  64: "4rem",
  72: "4.5rem",
  76: "4.75rem",
  80: "5rem",
  88: "5.5rem",
  96: "6rem",
  100: "6.25rem",
  104: "6.5rem",
  106: "6.625",
  112: "7rem",
  128: "8rem",
  136: "8.5rem",
  142: "8.875rem",
  152: "9.5rem",
  168: "10.5rem",
  176: "11rem",
  182: "11.375rem",
  185: "11.563rem",
  200: "12.5rem",
  330: "20.625rem",
  352: "22rem",
  220: "13.75rem",
  250: "15.625rem",
  400: "25rem",
  432: "27rem",
  512: "32rem",
  640: "40rem",
  768: "48rem",
  "1/4": "25%",
  "1/2": "50%",
  "3/4": "75%",
  full: "100%",
};
