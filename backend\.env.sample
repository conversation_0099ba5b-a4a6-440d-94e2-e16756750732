
# VideoDB Integration
VIDEO_DB_API_KEY=

# Database
DB_TYPE=  # postgres or sqlite

    # PostgreSQL Configuration (required if DB_TYPE=postgres)
POSTGRES_DB=
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_HOST=
POSTGRES_PORT=

    # SQLite Configuration (required if DB_TYPE=sqlite)
SQLITE_DB_PATH=

# LLM Integrations
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GOOGLEAI_API_KEY=

# Tools
REPLICATE_API_TOKEN=

# Brandkit Agent
INTRO_VIDEO_ID=
OUTRO_VIDEO_ID=
BRAND_IMAGE_ID=

# Profanity Remover Agent
BEEP_AUDIO_ID=

# Slack Agent
SLACK_CHANNEL_NAME=
SLACK_BOT_TOKEN=

# Dubbing & Audio Generation Agent
ELEVENLABS_API_KEY=
BEATOVEN_API_KEY=

# Video Generation Agent
## StabilityAI
STABILITYAI_API_KEY=

## Kling AI
KLING_AI_ACCESS_API_KEY=
KLING_AI_SECRET_API_KEY=

# FAL AI
FAL_KEY=

# Composio Agent
# https://composio.dev/tools
COMPOSIO_API_KEY=
COMPOSIO_APPS=["HACKERNEWS"]

# Web Search Agent
SERP_API_KEY=

# Devzery API Test
DEVZERY_API_KEY=
DEVZERY_SOURCE_NAME=