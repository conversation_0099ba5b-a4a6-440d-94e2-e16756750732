name: Build and Publish Sphinx Docs 

on:
  push:
    branches: [main]

  workflow_dispatch:
permissions:
  contents: write
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Set up Python 3.12
        uses: actions/setup-python@v2
        with:
          python-version: "3.12"

      - name: Install dependencies
        run: |
          cd backend
          python -m venv venv
          source venv/bin/activate
          pip install -r requirements.txt
          pip install -r requirements-dev.txt

      - name: Build and Publish HTML
        run: |
          source backend/venv/bin/activate
          mkdocs gh-deploy --force