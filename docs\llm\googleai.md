## GoogleAI

GoogleAI extends the Base LLM and implements the Google Gemini API.

### Google<PERSON>I Config

GoogleAI Config is the configuration object for Google Gemini. It is used to configure Google Gemini and is passed to GoogleAI when it is created.

::: director.llm.googleai.GoogleAIConfig

### Google<PERSON>I Interface

GoogleAI is the LLM used by the agents and tools. It is used to generate responses to messages.

::: director.llm.googleai.GoogleAI
